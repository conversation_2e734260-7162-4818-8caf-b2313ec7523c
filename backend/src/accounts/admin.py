from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.forms import UserChangeForm, UserCreationForm
from django.utils.html import format_html
from .models import (
    User,
    UserWallet,
    UserPoints,
    UserLocationUpdate,
    UserBalanceUpdate,
    UserPointsUpdate,
)


class CustomUserChangeForm(UserChangeForm):
    class Meta(UserChangeForm.Meta):
        model = User


class CustomUserCreationForm(UserCreationForm):
    class Meta(UserCreationForm.Meta):
        model = User


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    form = CustomUserChangeForm
    add_form = CustomUserCreationForm

    list_display = (
        "username",
        "email",
        "first_name",
        "last_name",
        "role",
        "phone_number",
        "is_active",
        "date_joined",
    )
    list_filter = ("role", "is_active", "is_staff", "is_superuser", "date_joined")
    search_fields = ("username", "email", "first_name", "last_name", "phone_number")
    ordering = ("-date_joined",)
    readonly_fields = ("date_joined", "last_login")

    fieldsets = (
        (None, {"fields": ("username", "password")}),
        (
            "Personal Information",
            {"fields": ("email", "first_name", "last_name", "phone_number")},
        ),
        (
            "Role & Permissions",
            {
                "fields": (
                    "role",
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                )
            },
        ),
        (
            "Delivery Employee Settings",
            {
                "fields": (
                    "commission_fixed_rate",
                    "current_location_lat",
                    "current_location_lng",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Important Dates",
            {"fields": ("date_joined", "last_login"), "classes": ("collapse",)},
        ),
    )

    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "username",
                    "email",
                    "password1",
                    "password2",
                    "role",
                    "is_active",
                ),
            },
        ),
    )


@admin.register(UserWallet)
class UserWalletAdmin(admin.ModelAdmin):
    list_display = ("user", "balance", "formatted_balance")
    list_filter = ("balance",)
    search_fields = (
        "user__username",
        "user__email",
        "user__first_name",
        "user__last_name",
    )
    ordering = ("-balance",)

    def formatted_balance(self, obj):
        return f"${obj.balance:,.2f}"

    formatted_balance.short_description = "Formatted Balance"


@admin.register(UserPoints)
class UserPointsAdmin(admin.ModelAdmin):
    list_display = ("user", "points", "points_last_update")
    list_filter = ("points", "points_last_update")
    search_fields = (
        "user__username",
        "user__email",
        "user__first_name",
        "user__last_name",
    )
    ordering = ("-points",)
    readonly_fields = ("points_last_update",)


@admin.register(UserLocationUpdate)
class UserLocationUpdateAdmin(admin.ModelAdmin):
    list_display = ("user", "location_lat", "location_lng", "update_time")
    list_filter = ("update_time",)
    search_fields = (
        "user__username",
        "user__email",
        "user__first_name",
        "user__last_name",
    )
    ordering = ("-update_time",)
    readonly_fields = ("update_time",)

    fieldsets = (
        ("User Information", {"fields": ("user",)}),
        ("Location Data", {"fields": ("location_lat", "location_lng")}),
        ("Timestamp", {"fields": ("update_time",), "classes": ("collapse",)}),
    )


@admin.register(UserBalanceUpdate)
class UserBalanceUpdateAdmin(admin.ModelAdmin):
    list_display = ("user", "balance_change", "formatted_balance_change", "update_time")
    list_filter = ("balance_change", "update_time")
    search_fields = (
        "user__username",
        "user__email",
        "user__first_name",
        "user__last_name",
    )
    ordering = ("-update_time",)
    readonly_fields = ("update_time",)

    def formatted_balance_change(self, obj):
        if obj.balance_change >= 0:
            return f"+${obj.balance_change:,.2f}"
        return f"-${abs(obj.balance_change):,.2f}"

    formatted_balance_change.short_description = "Balance Change"


@admin.register(UserPointsUpdate)
class UserPointsUpdateAdmin(admin.ModelAdmin):
    list_display = ("user", "points_change", "formatted_points_change", "update_time")
    list_filter = ("points_change", "update_time")
    search_fields = (
        "user__username",
        "user__email",
        "user__first_name",
        "user__last_name",
    )
    ordering = ("-update_time",)
    readonly_fields = ("update_time",)

    def formatted_points_change(self, obj):
        if obj.points_change >= 0:
            return f"+{obj.points_change}"
        return f"-{abs(obj.points_change)}"

    formatted_points_change.short_description = "Points Change"
