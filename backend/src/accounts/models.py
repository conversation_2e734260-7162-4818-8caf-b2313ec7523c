from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from core.utils import MyModel


class Role(models.TextChoices):
    ADMIN = "admin", "Admin"
    MANAGER = "manager", "Manager"
    EMPLOYEE = "employee", "Employee"


class User(AbstractUser, MyModel):
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    role = models.CharField(max_length=16, choices=Role.choices, default=Role.EMPLOYEE)

    # Delivery employee commission rate
    commission_fixed_rate = models.DecimalField(
        max_digits=6,
        decimal_places=0,
        validators=[MinValueValidator(0), MaxValueValidator(100000)],
        null=True,
        blank=True,
        help_text=_("Commission fixed rate for delivery employees"),
    )

    # Delivery employee location
    current_location_lat = models.DecimalField(
        max_digits=9, decimal_places=6, null=True, blank=True
    )
    current_location_lng = models.DecimalField(
        max_digits=9, decimal_places=6, null=True, blank=True
    )

    def __str__(self):
        return self.username

    class Meta:
        db_table = "users"
        verbose_name = "User"


class UserWallet(MyModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="wallet")
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    def __str__(self):
        return f"{self.user} - {self.balance}"

    class Meta:
        db_table = "user_wallets"
        verbose_name = "Wallet"


class UserPoints(MyModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="points")
    points = models.IntegerField()
    points_last_update = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.user} - {self.points}"

    class Meta:
        db_table = "user_points"
        verbose_name = "Point"


class UserLocationUpdate(MyModel):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="location_updates"
    )
    location_lat = models.DecimalField(max_digits=9, decimal_places=6)
    location_lng = models.DecimalField(max_digits=9, decimal_places=6)
    update_time = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user} - {self.location_lat}, {self.location_lng}"

    class Meta:
        db_table = "user_location_updates"
        verbose_name = "Location Update"


class UserBalanceUpdate(MyModel):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="balance_updates"
    )
    balance_change = models.DecimalField(max_digits=10, decimal_places=2)
    update_time = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user} - {self.balance_change}"

    class Meta:
        db_table = "user_balance_updates"
        verbose_name = "Balance Update"


class UserPointsUpdate(MyModel):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="points_updates"
    )
    points_change = models.IntegerField()
    update_time = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user} - {self.points_change}"

    class Meta:
        db_table = "user_points_updates"
        verbose_name = "Points Updates"
