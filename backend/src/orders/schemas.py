from typing import List, Optional
from datetime import datetime
from ninja import Schema
from enum import Enum


class OrderHandlingStatusSchema(str, Enum):
    PENDING = "PENDING"
    ASSIGNED = "ASSIGNED"
    PROCESSING = "PROCESSING"
    CANCELLED = "CANCELLED"
    DELIVERED = "DELIVERED"


class CompanySchema(Schema):
    id: str
    office_id: str
    code: Optional[str]
    name: str
    address: str
    phone: str
    color_code: str
    created_at: datetime
    updated_at: datetime


class CreateCompanySchema(Schema):
    office_id: str
    code: Optional[str]
    name: str
    address: str
    phone: str
    color_code: str


class UpdateCompanySchema(Schema):
    code: Optional[str]
    name: str
    address: str
    phone: str
    color_code: str


class CompanyChannelSchema(Schema):
    id: str
    merchant_id: str
    office_id: str
    company_id: Optional[str]
    name: str
    notes: str
    channel_whatsapp_number: str
    created_at: datetime
    updated_at: datetime


class CreateCompanyChannelSchema(Schema):
    merchant_id: str
    office_id: str
    company_id: Optional[str]
    name: str
    notes: str
    channel_whatsapp_number: str


class UpdateCompanyChannelSchema(Schema):
    company_id: Optional[str]
    name: str
    notes: str
    channel_whatsapp_number: str


class OrderCancellationReasonTemplateSchema(Schema):
    id: str
    office_id: Optional[str]
    name: str
    description: str
    order_default_handling_status: Optional[OrderHandlingStatusSchema]
    just_delivery_commission_rate: bool
    commission_fixed_rate: Optional[float]
    percentage_of_order_total_price: Optional[float]
    created_at: datetime
    updated_at: datetime


class CreateOrderCancellationReasonTemplateSchema(Schema):
    office_id: Optional[str]
    name: str
    description: str
    order_default_handling_status: Optional[OrderHandlingStatusSchema]
    just_delivery_commission_rate: bool
    commission_fixed_rate: Optional[float]
    percentage_of_order_total_price: Optional[float]


class UpdateOrderCancellationReasonTemplateSchema(Schema):
    name: str
    description: str
    order_default_handling_status: Optional[OrderHandlingStatusSchema]
    just_delivery_commission_rate: bool
    commission_fixed_rate: Optional[float]
    percentage_of_order_total_price: Optional[float]


class OrderSchema(Schema):
    id: str
    office_id: str
    code: str
    notes: str
    total_price: Optional[float]
    customer_name: str
    customer_phone: str
    customer_address: str
    customer_company_id: Optional[str]
    breakable: bool
    deadline_date: Optional[datetime]
    commission_fixed_rate: Optional[float]
    assigned_to_id: Optional[str]
    assigned_at: Optional[datetime]
    final_customer_payment: Optional[float]
    handling_status: OrderHandlingStatusSchema
    cancellation_reason_template_id: Optional[str]
    cancellation_reason: Optional[str]
    created_at: datetime
    updated_at: datetime


class CreateOrderSchema(Schema):
    office_id: str
    code: str
    notes: str
    total_price: Optional[float]
    customer_name: str
    customer_phone: str
    customer_address: str
    customer_company_id: Optional[str]
    breakable: bool
    deadline_date: Optional[datetime]
    commission_fixed_rate: Optional[float]
    assigned_to_id: Optional[str]
    final_customer_payment: Optional[float]
    handling_status: OrderHandlingStatusSchema
    cancellation_reason_template_id: Optional[str]
    cancellation_reason: Optional[str]


class UpdateOrderSchema(Schema):
    code: str
    notes: str
    total_price: Optional[float]
    customer_name: str
    customer_phone: str
    customer_address: str
    customer_company_id: Optional[str]
    breakable: bool
    deadline_date: Optional[datetime]
    commission_fixed_rate: Optional[float]
    assigned_to_id: Optional[str]
    final_customer_payment: Optional[float]
    handling_status: OrderHandlingStatusSchema
    cancellation_reason_template_id: Optional[str]
    cancellation_reason: Optional[str]


class OrderStatusHistorySchema(Schema):
    id: str
    order_id: str
    status: OrderHandlingStatusSchema
    notes: str
    created_by_id: str
    created_at: datetime
    updated_at: datetime


class OrderAssigneeHistorySchema(Schema):
    id: str
    order_id: str
    assignee_id: str
    assigned_by_id: str
    created_at: datetime
    updated_at: datetime


# Response schemas for pagination
class CompanyListResponseSchema(Schema):
    companies: List[CompanySchema]
    total: int
    page: int
    page_size: int


class CompanyChannelListResponseSchema(Schema):
    channels: List[CompanyChannelSchema]
    total: int
    page: int
    page_size: int


class OrderCancellationReasonTemplateListResponseSchema(Schema):
    templates: List[OrderCancellationReasonTemplateSchema]
    total: int
    page: int
    page_size: int


class OrderListResponseSchema(Schema):
    orders: List[OrderSchema]
    total: int
    page: int
    page_size: int


class OrderStatusHistoryListResponseSchema(Schema):
    history: List[OrderStatusHistorySchema]
    total: int
    page: int
    page_size: int


class OrderAssigneeHistoryListResponseSchema(Schema):
    history: List[OrderAssigneeHistorySchema]
    total: int
    page: int
    page_size: int
