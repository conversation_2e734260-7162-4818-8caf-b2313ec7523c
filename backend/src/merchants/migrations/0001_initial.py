# Generated by Django 5.2.4 on 2025-07-18 17:07

import core.utils
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Merchant',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('address', models.TextField()),
                ('phone_number', models.CharField(max_length=15)),
                ('email', models.EmailField(max_length=254)),
                ('subscription_start_date', models.DateTimeField(blank=True, null=True)),
                ('subscription_end_date', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'merchants',
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('duration', models.IntegerField()),
                ('duration_unit', models.CharField(choices=[('day', 'Day'), ('week', 'Week'), ('month', 'Month'), ('year', 'Year')], default='month', max_length=16)),
            ],
            options={
                'db_table': 'subscription_plans',
            },
        ),
        migrations.CreateModel(
            name='Office',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('slug', models.SlugField(unique=True)),
                ('address', models.TextField()),
                ('phone_number', models.CharField(max_length=15)),
                ('email', models.EmailField(max_length=254)),
                ('merchant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='offices', to='merchants.merchant')),
            ],
            options={
                'db_table': 'offices',
            },
        ),
        migrations.CreateModel(
            name='OfficeEmployee',
            fields=[
                ('id', models.CharField(default=core.utils.generate_ulid, editable=False, max_length=26, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('office', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='merchants.office')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='offices', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'office_employees',
            },
        ),
        migrations.AddField(
            model_name='merchant',
            name='subscription_plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='merchants', to='merchants.subscriptionplan'),
        ),
    ]
