from django.db import transaction
from django.http import HttpRequest
from django.core.paginator import Paginator
from ninja import Router
from accounts.models import Role, User
from accounts.schemas import UserSchema
from .models import (
    SubscriptionPlan,
    Merchant,
    Office,
    OfficeEmployee,
)
from .schemas import (
    SubscriptionPlanSchema,
    CreateSubscriptionPlanSchema,
    UpdateSubscriptionPlanSchema,
    MerchantSchema,
    CreateMerchantSchema,
    UpdateMerchantSchema,
    OfficeSchema,
    CreateOfficeSchema,
    UpdateOfficeSchema,
    OfficeEmployeeSchema,
    CreateOfficeEmployeeSchema,
    SubscriptionPlanListResponseSchema,
    MerchantListResponseSchema,
    OfficeListResponseSchema,
    OfficeEmployeeListResponseSchema,
)
from core.middleware import AuthKey

router = Router(tags=["merchants"])


# GET /api/merchants/{merchant_id}
@router.get("/{merchant_id}", auth=AuthKey)
def get_merchant(request: HttpRequest, merchant_id: str) -> MerchantSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {"message": "You don't have permission to view merchants"}

    # Check if user has access to this merchant
    if request.user.role == Role.MANAGER:
        user_merchants = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office__merchant_id", flat=True
        )
        if merchant_id not in user_merchants:
            return 403, {"message": "You don't have access to this merchant"}

    merchant = Merchant.objects.get(id=merchant_id)
    return MerchantSchema.from_orm(merchant)


# PUT /api/merchants/{merchant_id}
@router.put("/{merchant_id}", auth=AuthKey)
def update_merchant(
    request: HttpRequest, merchant_id: str, data: UpdateMerchantSchema
) -> MerchantSchema:
    if request.user.role != Role.ADMIN:
        return 403, {"message": "You don't have permission to update merchants"}

    merchant = Merchant.objects.get(id=merchant_id)
    subscription_plan = SubscriptionPlan.objects.get(id=data.subscription_plan_id)

    merchant.name = data.name
    merchant.description = data.description
    merchant.address = data.address
    merchant.phone_number = data.phone_number
    merchant.email = data.email
    merchant.subscription_plan = subscription_plan
    merchant.save()

    return MerchantSchema.from_orm(merchant)


# DELETE /api/merchants/{merchant_id}
@router.delete("/{merchant_id}", auth=AuthKey)
def delete_merchant(request: HttpRequest, merchant_id: str):
    if request.user.role != Role.ADMIN:
        return 403, {"message": "You don't have permission to delete merchants"}

    merchant = Merchant.objects.get(id=merchant_id)
    merchant.delete()
    return 200, {"message": "Merchant deleted successfully"}


# Office endpoints
# GET /api/merchants/offices/
@router.get("/offices/", auth=AuthKey, response=OfficeListResponseSchema)
def list_offices(
    request: HttpRequest,
    page: int = 1,
    page_size: int = 10,
    merchant_id: str = None,
):
    # Check if user has permission to list offices
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {"message": "You don't have permission to list offices"}

    # Build queryset based on user role
    if request.user.role == Role.ADMIN:
        offices = Office.objects.all()
    else:
        # Managers can only see offices where they are employees
        user_offices = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office_id", flat=True
        )
        offices = Office.objects.filter(id__in=user_offices)

    # Apply filters
    if merchant_id:
        offices = offices.filter(merchant_id=merchant_id)

    offices = offices.order_by("name")

    paginator = Paginator(offices, page_size)
    page_obj = paginator.get_page(page)

    return OfficeListResponseSchema(
        offices=[OfficeSchema.from_orm(office) for office in page_obj],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/merchants/offices/
@router.post("/offices/", auth=AuthKey)
def create_office(request: HttpRequest, data: CreateOfficeSchema) -> OfficeSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {"message": "You don't have permission to create offices"}

    # Check if user has access to this merchant
    if request.user.role == Role.MANAGER:
        user_merchants = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office__merchant_id", flat=True
        )
        if data.merchant_id not in user_merchants:
            return 403, {"message": "You don't have access to this merchant"}

    try:
        merchant = Merchant.objects.get(id=data.merchant_id)
        office = Office.objects.create(
            merchant=merchant,
            name=data.name,
            slug=data.slug,
            address=data.address,
            phone_number=data.phone_number,
            email=data.email,
        )
        return OfficeSchema.from_orm(office)
    except Exception as e:
        return 400, {"message": str(e)}


# GET /api/merchants/offices/{office_id}
@router.get("/offices/{office_id}", auth=AuthKey)
def get_office(request: HttpRequest, office_id: str) -> OfficeSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {"message": "You don't have permission to view offices"}

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office_id", flat=True
        )
        if office_id not in user_offices:
            return 403, {"message": "You don't have access to this office"}

    office = Office.objects.get(id=office_id)
    return OfficeSchema.from_orm(office)


# PUT /api/merchants/offices/{office_id}
@router.put("/offices/{office_id}", auth=AuthKey)
def update_office(
    request: HttpRequest, office_id: str, data: UpdateOfficeSchema
) -> OfficeSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {"message": "You don't have permission to update offices"}

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office_id", flat=True
        )
        if office_id not in user_offices:
            return 403, {"message": "You don't have access to this office"}

    office = Office.objects.get(id=office_id)
    office.name = data.name
    office.slug = data.slug
    office.address = data.address
    office.phone_number = data.phone_number
    office.email = data.email
    office.save()

    return OfficeSchema.from_orm(office)


# DELETE /api/merchants/offices/{office_id}
@router.delete("/offices/{office_id}", auth=AuthKey)
def delete_office(request: HttpRequest, office_id: str):
    if request.user.role != Role.ADMIN:
        return 403, {"message": "You don't have permission to delete offices"}

    office = Office.objects.get(id=office_id)
    office.delete()
    return 200, {"message": "Office deleted successfully"}


# Office Employee endpoints


# GET /api/merchants/offices/employees/
@router.get("/offices/get-by-phone-number/", response=OfficeEmployeeListResponseSchema)
def list_employees_offices_by_phone_number(
    request: HttpRequest, employee_phone_number: str
):
    # get the offices where the employee is working
    offices = OfficeEmployee.objects.filter(
        user__phone_number=employee_phone_number
    ).values_list("office_id", flat=True)
    # get the offices where the employee is working
    offices = Office.objects.filter(id__in=offices)
    # return the offices
    return OfficeListResponseSchema(
        total=offices.count(),
        page=1,
        page_size=offices.count(),
        offices=[OfficeSchema.from_orm(office) for office in offices],
    )


# GET /api/merchants/offices/{office_id}/employees/
@router.get(
    "/offices/{office_id}/employees/",
    auth=AuthKey,
    response=OfficeEmployeeListResponseSchema,
)
def list_office_employees(
    request: HttpRequest,
    office_id: str,
    page: int = 1,
    page_size: int = 10,
):
    # Check if user has permission to list employees
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {"message": "You don't have permission to list employees"}

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office_id", flat=True
        )
        if office_id not in user_offices:
            return 403, {"message": "You don't have access to this office"}

    employees = (
        OfficeEmployee.objects.filter(office_id=office_id)
        .select_related("user")
        .order_by("user__username")
    )

    paginator = Paginator(employees, page_size)
    page_obj = paginator.get_page(page)

    return OfficeEmployeeListResponseSchema(
        employees=[
            OfficeEmployeeSchema(
                id=str(employee.id),
                office_id=str(employee.office_id),
                user_id=str(employee.user.id),
                user=UserSchema.from_orm(employee.user),
            )
            for employee in page_obj
        ],
        total=paginator.count,
        page=page,
        page_size=page_size,
    )


# POST /api/merchants/offices/{office_id}/employees/
@router.post("/offices/{office_id}/employees/", auth=AuthKey)
def add_office_employee(
    request: HttpRequest, office_id: str, data: CreateOfficeEmployeeSchema
) -> OfficeEmployeeSchema:
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {"message": "You don't have permission to add employees"}

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office_id", flat=True
        )
        if office_id not in user_offices:
            return 403, {"message": "You don't have access to this office"}

    try:
        office = Office.objects.get(id=office_id)
        user = User.objects.get(id=data.user_id)

        # Check if employee already exists
        if OfficeEmployee.objects.filter(office=office, user=user).exists():
            return 400, {"message": "Employee already exists in this office"}

        employee = OfficeEmployee.objects.create(
            office=office,
            user=user,
        )
        return OfficeEmployeeSchema(
            id=str(employee.id),
            office_id=str(employee.office_id),
            user_id=str(employee.user.id),
            user=UserSchema.from_orm(employee.user),
        )
    except Exception as e:
        return 400, {"message": str(e)}


# DELETE /api/merchants/offices/{office_id}/employees/{employee_id}
@router.delete("/offices/{office_id}/employees/{employee_id}", auth=AuthKey)
def remove_office_employee(request: HttpRequest, office_id: str, employee_id: str):
    if request.user.role not in [Role.ADMIN, Role.MANAGER]:
        return 403, {"message": "You don't have permission to remove employees"}

    # Check if user has access to this office
    if request.user.role == Role.MANAGER:
        user_offices = OfficeEmployee.objects.filter(user=request.user).values_list(
            "office_id", flat=True
        )
        if office_id not in user_offices:
            return 403, {"message": "You don't have access to this office"}

    employee = OfficeEmployee.objects.get(id=employee_id, office_id=office_id)
    employee.delete()
    return 200, {"message": "Employee removed from office successfully"}
