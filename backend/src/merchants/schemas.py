from datetime import datetime
from ninja import Schema
from typing import List, Optional
from accounts.schemas import UserSchema


class SubscriptionPlanSchema(Schema):
    id: str
    name: str
    description: str
    price: float
    duration: int
    duration_unit: str


class CreateSubscriptionPlanSchema(Schema):
    name: str
    description: str
    price: float
    duration: int
    duration_unit: str


class UpdateSubscriptionPlanSchema(Schema):
    name: str
    description: str
    price: float
    duration: int
    duration_unit: str


class MerchantSchema(Schema):
    id: str
    name: str
    description: str
    address: str
    phone_number: str
    email: str
    subscription_plan: SubscriptionPlanSchema
    subscription_start_date: Optional[datetime]
    subscription_end_date: Optional[datetime]


class CreateMerchantSchema(Schema):
    name: str
    description: str
    address: str
    phone_number: str
    email: str
    subscription_plan_id: str


class UpdateMerchantSchema(Schema):
    name: str
    description: str
    address: str
    phone_number: str
    email: str
    subscription_plan_id: str


class OfficeSchema(Schema):
    id: str
    merchant_id: str
    name: str
    slug: str
    address: str
    phone_number: str
    email: str


class CreateOfficeSchema(Schema):
    merchant_id: str
    name: str
    slug: str
    address: str
    phone_number: str
    email: str


class UpdateOfficeSchema(Schema):
    name: str
    slug: str
    address: str
    phone_number: str
    email: str


class OfficeEmployeeSchema(Schema):
    id: str
    office_id: str
    user_id: str
    user: UserSchema


class CreateOfficeEmployeeSchema(Schema):
    office_id: str
    user_id: str


# List response schemas
class SubscriptionPlanListResponseSchema(Schema):
    subscription_plans: List[SubscriptionPlanSchema]
    total: int
    page: int
    page_size: int


class MerchantListResponseSchema(Schema):
    merchants: List[MerchantSchema]
    total: int
    page: int
    page_size: int


class OfficeListResponseSchema(Schema):
    offices: List[OfficeSchema]
    total: int
    page: int
    page_size: int


class OfficeEmployeeListResponseSchema(Schema):
    employees: List[OfficeEmployeeSchema]
    total: int
    page: int
    page_size: int
