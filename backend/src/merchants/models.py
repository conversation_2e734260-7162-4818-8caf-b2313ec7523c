from django.db import models
from django.db.models import QuerySet
from accounts.models import User
from core.utils import MyModel


class DurationUnit(models.TextChoices):
    DAY = "day", "Day"
    WEEK = "week", "Week"
    MONTH = "month", "Month"
    YEAR = "year", "Year"


class SubscriptionType(models.TextChoices):
    FREE = "free", "Free"
    MONTHLY = "monthly", "Monthly"
    YEARLY = "yearly", "Yearly"
    ONE_TIME = "one_time", "One Time"


class SubscriptionPlan(MyModel):
    name = models.CharField(max_length=255)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    duration = models.IntegerField()
    duration_unit = models.CharField(
        max_length=16, choices=DurationUnit.choices, default=DurationUnit.MONTH
    )

    def __str__(self) -> str:
        return str(self.name)

    class Meta:
        db_table = "subscription_plans"
        verbose_name = "Subscription Plan"


class Merchant(MyModel):
    name = models.CharField(max_length=255)
    description = models.TextField()
    address = models.TextField()
    phone_number = models.CharField(max_length=15)
    email = models.EmailField()

    # Subscriptions
    subscription_plan = models.ForeignKey(
        SubscriptionPlan, on_delete=models.CASCADE, related_name="merchants"
    )
    subscription_start_date = models.DateTimeField(null=True, blank=True)
    subscription_end_date = models.DateTimeField(null=True, blank=True)

    @property
    def offices(self) -> QuerySet["Office"]:
        return Office.objects.filter(merchant=self)

    def __str__(self) -> str:
        return str(self.name)

    class Meta:
        db_table = "merchants"
        verbose_name = "Merchant"


class Office(MyModel):
    merchant = models.ForeignKey(
        Merchant, on_delete=models.CASCADE, related_name="offices"
    )
    name = models.CharField(max_length=255)
    slug = models.SlugField(unique=True)
    address = models.TextField()
    phone_number = models.CharField(max_length=15)
    email = models.EmailField()

    @property
    def employees(self) -> QuerySet["OfficeEmployee"]:
        return OfficeEmployee.objects.filter(office=self)

    def __str__(self) -> str:
        return str(self.name)

    class Meta:
        db_table = "offices"
        verbose_name = "Office"


class OfficeEmployee(MyModel):
    office = models.ForeignKey(
        Office, on_delete=models.CASCADE, related_name="employees"
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="offices")

    def __str__(self) -> str:
        return f"{self.user} - {self.office}"

    class Meta:
        db_table = "office_employees"
        verbose_name = "Office Employee"
