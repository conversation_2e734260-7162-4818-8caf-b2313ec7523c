from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from .models import SubscriptionPlan, Merchant, Office, OfficeEmployee


@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = ("name", "price", "duration", "duration_unit", "formatted_price")
    list_filter = ("duration_unit", "price")
    search_fields = ("name", "description")
    ordering = ("price",)

    def formatted_price(self, obj):
        return f"${obj.price:,.2f}"

    formatted_price.short_description = "Formatted Price"

    fieldsets = (
        ("Basic Information", {"fields": ("name", "description")}),
        ("Pricing & Duration", {"fields": ("price", "duration", "duration_unit")}),
    )


class OfficeEmployeeInline(admin.TabularInline):
    model = OfficeEmployee
    extra = 1
    fk_name = "office"


@admin.register(Office)
class OfficeAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "merchant",
        "slug",
        "phone_number",
        "email",
        "employee_count",
    )
    list_filter = ("merchant",)
    search_fields = ("name", "merchant__name", "address", "phone_number", "email")
    ordering = ("merchant__name", "name")
    prepopulated_fields = {"slug": ("name",)}

    inlines = [OfficeEmployeeInline]

    def employee_count(self, obj):
        return obj.employees.count()

    employee_count.short_description = "Employees"

    fieldsets = (
        ("Basic Information", {"fields": ("merchant", "name", "slug")}),
        ("Contact Information", {"fields": ("address", "phone_number", "email")}),
    )


@admin.register(Merchant)
class MerchantAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "subscription_plan",
        "subscription_status",
        "office_count",
        "phone_number",
        "email",
    )
    list_filter = (
        "subscription_plan",
        "subscription_start_date",
        "subscription_end_date",
    )
    search_fields = ("name", "description", "address", "phone_number", "email")
    ordering = ("name",)
    readonly_fields = ("office_count_display",)

    def subscription_status(self, obj):
        if not obj.subscription_start_date:
            return "Not Started"
        if obj.subscription_end_date and obj.subscription_end_date < timezone.now():
            return "Expired"
        return "Active"

    subscription_status.short_description = "Subscription Status"

    def office_count(self, obj):
        return obj.offices.count()

    office_count.short_description = "Offices"

    def office_count_display(self, obj):
        count = obj.offices.count()
        if count > 0:
            offices_list = ", ".join([office.name for office in obj.offices.all()])
            return format_html(
                "<strong>{} offices:</strong><br>{}", count, offices_list
            )
        return "No offices"

    office_count_display.short_description = "Office Details"

    fieldsets = (
        ("Basic Information", {"fields": ("name", "description")}),
        ("Contact Information", {"fields": ("address", "phone_number", "email")}),
        (
            "Subscription",
            {
                "fields": (
                    "subscription_plan",
                    "subscription_start_date",
                    "subscription_end_date",
                )
            },
        ),
        (
            "Office Information",
            {"fields": ("office_count_display",), "classes": ("collapse",)},
        ),
    )


@admin.register(OfficeEmployee)
class OfficeEmployeeAdmin(admin.ModelAdmin):
    list_display = ("user", "office", "merchant", "user_role", "user_email")
    list_filter = ("office__merchant", "user__role")
    search_fields = (
        "user__username",
        "user__email",
        "user__first_name",
        "user__last_name",
        "office__name",
    )
    ordering = ("office__merchant__name", "office__name", "user__username")

    def merchant(self, obj):
        return obj.office.merchant.name

    merchant.short_description = "Merchant"

    def user_role(self, obj):
        return obj.user.get_role_display()

    user_role.short_description = "User Role"

    def user_email(self, obj):
        return obj.user.email

    user_email.short_description = "User Email"

    fieldsets = (("Employee Information", {"fields": ("user", "office")}),)

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return True
