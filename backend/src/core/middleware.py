import jwt
from core import settings
from accounts.models import User
from merchants.models import Office
from dataclasses import dataclass
from ninja.security import APIKeyQuery


@dataclass
class JWTToken:
    token: str
    user: User
    office: Office


def create_jwt_token(user: User, office: Office) -> JWTToken:
    return J<PERSON>TToken(
        token=jwt.encode(
            {"user_id": user.id, "office_id": office.id},
            settings.SECRET_KEY,
            algorithm="HS256",
        ),
        user=user,
        office=office,
    )


def decode_jwt_token(token: str) -> JWTToken:
    return JWTToken(
        token=token,
        user=User.objects.get(
            id=jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])["user_id"],
            office=Office.objects.get(
                id=jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])[
                    "office_id"
                ]
            ),
        ),
    )


class ApiKey(APIKeyQuery):
    param_name = "token"

    def authenticate(self, request, key):
        try:
            user = decode_jwt_token(key).user
            request.user = user
            return user
        except Exception:
            pass


AuthKey = ApiKey()
