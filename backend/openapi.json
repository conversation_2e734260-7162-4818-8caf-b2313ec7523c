{"openapi": "3.1.0", "info": {"title": "NinjaAPI", "version": "1.0.0", "description": ""}, "paths": {"/api/accounts/login": {"post": {"operationId": "accounts_apis_login", "summary": "<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseSchema"}}}}}, "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginSchema"}}}, "required": true}}}, "/api/accounts/logout": {"post": {"operationId": "accounts_apis_logout", "summary": "Logout", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}}, "/api/accounts/me": {"get": {"operationId": "accounts_apis_get_me", "summary": "Get Me", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}}, "/api/accounts/": {"get": {"operationId": "accounts_apis_list_users", "summary": "List Users", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "role", "schema": {"title": "Role", "type": "string"}, "required": false}, {"in": "query", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserListResponseSchema"}}}}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}, "post": {"operationId": "accounts_apis_create_user", "summary": "Create User", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/accounts/{user_id}": {"get": {"operationId": "accounts_apis_get_user", "summary": "Get User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}, "put": {"operationId": "accounts_apis_update_user", "summary": "Update User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}, "delete": {"operationId": "accounts_apis_delete_user", "summary": "Delete User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}}, "/api/accounts/me/location": {"post": {"operationId": "accounts_apis_update_location", "summary": "Update Location", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationUpdateSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/accounts/me/location/history": {"get": {"operationId": "accounts_apis_get_location_history", "summary": "Get Location History", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationHistoryResponseSchema"}}}}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}}, "/api/accounts/me/offices": {"get": {"operationId": "accounts_apis_get_my_offices", "summary": "Get My Offices", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "Get all offices where the current user is an employee", "tags": ["accounts"], "security": [{"ApiKey": []}]}}, "/api/accounts/me/wallet": {"get": {"operationId": "accounts_apis_get_me_wallet", "summary": "Get Me Wallet", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}, "put": {"operationId": "accounts_apis_update_me_wallet", "summary": "Update Me Wallet", "parameters": [{"in": "query", "name": "amount", "schema": {"title": "Amount", "type": "number"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}}, "/api/accounts/me/points": {"get": {"operationId": "accounts_apis_get_me_points", "summary": "Get Me Points", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}, "post": {"operationId": "accounts_apis_add_points", "summary": "Add Points", "parameters": [{"in": "query", "name": "amount", "schema": {"title": "Amount", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["accounts"], "security": [{"ApiKey": []}]}}, "/api/merchants/subscription-plans/": {"get": {"operationId": "merchants_apis_list_subscription_plans", "summary": "List Subscription Plans", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionPlanListResponseSchema"}}}}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}, "post": {"operationId": "merchants_apis_create_subscription_plan", "summary": "Create Subscription Plan", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSubscriptionPlanSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/merchants/subscription-plans/{plan_id}": {"get": {"operationId": "merchants_apis_get_subscription_plan", "summary": "Get Subscription Plan", "parameters": [{"in": "path", "name": "plan_id", "schema": {"title": "Plan Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}, "put": {"operationId": "merchants_apis_update_subscription_plan", "summary": "Update Subscription Plan", "parameters": [{"in": "path", "name": "plan_id", "schema": {"title": "Plan Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSubscriptionPlanSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}, "delete": {"operationId": "merchants_apis_delete_subscription_plan", "summary": "Delete Subscription Plan", "parameters": [{"in": "path", "name": "plan_id", "schema": {"title": "Plan Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}}, "/api/merchants/": {"get": {"operationId": "merchants_apis_list_merchants", "summary": "List Merchants", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MerchantListResponseSchema"}}}}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}, "post": {"operationId": "merchants_apis_create_merchant", "summary": "Create Merchant", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMerchantSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/merchants/{merchant_id}": {"get": {"operationId": "merchants_apis_get_merchant", "summary": "Get Merchant", "parameters": [{"in": "path", "name": "merchant_id", "schema": {"title": "Merchant Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}, "put": {"operationId": "merchants_apis_update_merchant", "summary": "Update Merchant", "parameters": [{"in": "path", "name": "merchant_id", "schema": {"title": "Merchant Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMerchantSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}, "delete": {"operationId": "merchants_apis_delete_merchant", "summary": "Delete Merchant", "parameters": [{"in": "path", "name": "merchant_id", "schema": {"title": "Merchant Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}}, "/api/merchants/offices/": {"get": {"operationId": "merchants_apis_list_offices", "summary": "List Offices", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "merchant_id", "schema": {"title": "Merchant Id", "type": "string"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficeListResponseSchema"}}}}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}, "post": {"operationId": "merchants_apis_create_office", "summary": "Create Office", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOfficeSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/merchants/offices/{office_id}": {"get": {"operationId": "merchants_apis_get_office", "summary": "Get Office", "parameters": [{"in": "path", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}, "put": {"operationId": "merchants_apis_update_office", "summary": "Update Office", "parameters": [{"in": "path", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOfficeSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}, "delete": {"operationId": "merchants_apis_delete_office", "summary": "Delete Office", "parameters": [{"in": "path", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}}, "/api/merchants/offices/{office_id}/employees/": {"get": {"operationId": "merchants_apis_list_office_employees", "summary": "List Office Employees", "parameters": [{"in": "path", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": true}, {"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficeEmployeeListResponseSchema"}}}}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}, "post": {"operationId": "merchants_apis_add_office_employee", "summary": "Add Office Employee", "parameters": [{"in": "path", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOfficeEmployeeSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/merchants/offices/{office_id}/employees/{employee_id}": {"delete": {"operationId": "merchants_apis_remove_office_employee", "summary": "Remove Office Employee", "parameters": [{"in": "path", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": true}, {"in": "path", "name": "employee_id", "schema": {"title": "Employee Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["merchants"], "security": [{"ApiKey": []}]}}, "/api/orders/companies/": {"get": {"operationId": "orders_apis_list_companies", "summary": "List Companies", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyListResponseSchema"}}}}}, "tags": ["orders"], "security": [{"ApiKey": []}]}, "post": {"operationId": "orders_apis_create_company", "summary": "Create Company", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCompanySchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/orders/companies/{company_id}": {"get": {"operationId": "orders_apis_get_company", "summary": "Get Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "security": [{"ApiKey": []}]}, "put": {"operationId": "orders_apis_update_company", "summary": "Update Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCompanySchema"}}}, "required": true}, "security": [{"ApiKey": []}]}, "delete": {"operationId": "orders_apis_delete_company", "summary": "Delete Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "security": [{"ApiKey": []}]}}, "/api/orders/company-channels/": {"get": {"operationId": "orders_apis_list_company_channels", "summary": "List Company Channels", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "merchant_id", "schema": {"title": "Merchant Id", "type": "string"}, "required": false}, {"in": "query", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": false}, {"in": "query", "name": "company_id", "schema": {"title": "Company Id", "type": "string"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyChannelListResponseSchema"}}}}}, "tags": ["orders"], "security": [{"ApiKey": []}]}, "post": {"operationId": "orders_apis_create_company_channel", "summary": "Create Company Channel", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCompanyChannelSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/orders/company-channels/{channel_id}": {"get": {"operationId": "orders_apis_get_company_channel", "summary": "Get Company Channel", "parameters": [{"in": "path", "name": "channel_id", "schema": {"title": "Channel Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "security": [{"ApiKey": []}]}, "put": {"operationId": "orders_apis_update_company_channel", "summary": "Update Company Channel", "parameters": [{"in": "path", "name": "channel_id", "schema": {"title": "Channel Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCompanyChannelSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}, "delete": {"operationId": "orders_apis_delete_company_channel", "summary": "Delete Company Channel", "parameters": [{"in": "path", "name": "channel_id", "schema": {"title": "Channel Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "security": [{"ApiKey": []}]}}, "/api/orders/cancellation-templates/": {"get": {"operationId": "orders_apis_list_cancellation_templates", "summary": "List Cancellation Templates", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCancellationReasonTemplateListResponseSchema"}}}}}, "tags": ["orders"], "security": [{"ApiKey": []}]}, "post": {"operationId": "orders_apis_create_cancellation_template", "summary": "Create Cancellation Template", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderCancellationReasonTemplateSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/orders/cancellation-templates/{template_id}": {"get": {"operationId": "orders_apis_get_cancellation_template", "summary": "Get Cancellation Template", "parameters": [{"in": "path", "name": "template_id", "schema": {"title": "Template Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "security": [{"ApiKey": []}]}, "put": {"operationId": "orders_apis_update_cancellation_template", "summary": "Update Cancellation Template", "parameters": [{"in": "path", "name": "template_id", "schema": {"title": "Template Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderCancellationReasonTemplateSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}, "delete": {"operationId": "orders_apis_delete_cancellation_template", "summary": "Delete Cancellation Template", "parameters": [{"in": "path", "name": "template_id", "schema": {"title": "Template Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "security": [{"ApiKey": []}]}}, "/api/orders/": {"get": {"operationId": "orders_apis_list_orders", "summary": "List Orders", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "office_id", "schema": {"title": "Office Id", "type": "string"}, "required": false}, {"in": "query", "name": "status", "schema": {"title": "Status", "type": "string"}, "required": false}, {"in": "query", "name": "assigned_to_id", "schema": {"title": "Assigned To Id", "type": "string"}, "required": false}, {"in": "query", "name": "customer_company_id", "schema": {"title": "Customer Company Id", "type": "string"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderListResponseSchema"}}}}}, "tags": ["orders"], "security": [{"ApiKey": []}]}, "post": {"operationId": "orders_apis_create_order", "summary": "Create Order", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrderSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}}, "/api/orders/{order_id}": {"get": {"operationId": "orders_apis_get_order", "summary": "Get Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "security": [{"ApiKey": []}]}, "put": {"operationId": "orders_apis_update_order", "summary": "Update Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrderSchema"}}}, "required": true}, "security": [{"ApiKey": []}]}, "delete": {"operationId": "orders_apis_delete_order", "summary": "Delete Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "tags": ["orders"], "security": [{"ApiKey": []}]}}, "/api/orders/{order_id}/status-history/": {"get": {"operationId": "orders_apis_list_order_status_history", "summary": "List Order Status History", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "string"}, "required": true}, {"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderStatusHistoryListResponseSchema"}}}}}, "tags": ["orders"], "security": [{"ApiKey": []}]}}, "/api/orders/{order_id}/assignee-history/": {"get": {"operationId": "orders_apis_list_order_assignee_history", "summary": "List Order Assignee History", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "string"}, "required": true}, {"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 10, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderAssigneeHistoryListResponseSchema"}}}}}, "tags": ["orders"], "security": [{"ApiKey": []}]}}}, "components": {"schemas": {"LoginResponseSchema": {"properties": {"token": {"title": "Token", "type": "string"}, "user": {"$ref": "#/components/schemas/UserSchema"}}, "required": ["token", "user"], "title": "LoginResponseSchema", "type": "object"}, "UserSchema": {"properties": {"id": {"title": "Id", "type": "string"}, "first_name": {"title": "First Name", "type": "string"}, "last_name": {"title": "Last Name", "type": "string"}, "username": {"title": "Username", "type": "string"}, "email": {"title": "Email", "type": "string"}, "phone_number": {"title": "Phone Number", "type": "string"}, "role": {"title": "Role", "type": "string"}, "commission_fixed_rate": {"title": "Commission Fixed Rate", "type": "number"}, "current_location_lat": {"title": "Current Location Lat", "type": "number"}, "current_location_lng": {"title": "Current Location Lng", "type": "number"}}, "required": ["id", "first_name", "last_name", "username", "email", "phone_number", "role", "commission_fixed_rate", "current_location_lat", "current_location_lng"], "title": "UserSchema", "type": "object"}, "LoginSchema": {"properties": {"username": {"title": "Username", "type": "string"}, "password": {"title": "Password", "type": "string"}}, "required": ["username", "password"], "title": "LoginSchema", "type": "object"}, "UserListResponseSchema": {"properties": {"users": {"items": {"$ref": "#/components/schemas/UserSchema"}, "title": "Users", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["users", "total", "page", "page_size"], "title": "UserListResponseSchema", "type": "object"}, "CreateUserSchema": {"properties": {"first_name": {"title": "First Name", "type": "string"}, "last_name": {"title": "Last Name", "type": "string"}, "username": {"title": "Username", "type": "string"}, "email": {"title": "Email", "type": "string"}, "phone_number": {"title": "Phone Number", "type": "string"}, "office_id": {"title": "Office Id", "type": "string"}, "role": {"title": "Role", "type": "string"}, "commission_fixed_rate": {"title": "Commission Fixed Rate", "type": "number"}, "password": {"title": "Password", "type": "string"}}, "required": ["first_name", "last_name", "username", "email", "phone_number", "office_id", "role", "commission_fixed_rate", "password"], "title": "CreateUserSchema", "type": "object"}, "UpdateUserSchema": {"properties": {"first_name": {"title": "First Name", "type": "string"}, "last_name": {"title": "Last Name", "type": "string"}, "username": {"title": "Username", "type": "string"}, "email": {"title": "Email", "type": "string"}, "phone_number": {"title": "Phone Number", "type": "string"}, "role": {"title": "Role", "type": "string"}, "commission_fixed_rate": {"title": "Commission Fixed Rate", "type": "number"}, "current_location_lat": {"title": "Current Location Lat", "type": "number"}, "current_location_lng": {"title": "Current Location Lng", "type": "number"}}, "required": ["first_name", "last_name", "username", "email", "phone_number", "role", "commission_fixed_rate", "current_location_lat", "current_location_lng"], "title": "UpdateUserSchema", "type": "object"}, "LocationUpdateSchema": {"properties": {"latitude": {"title": "Latitude", "type": "number"}, "longitude": {"title": "Longitude", "type": "number"}}, "required": ["latitude", "longitude"], "title": "LocationUpdateSchema", "type": "object"}, "LocationHistoryResponseSchema": {"properties": {"locations": {"items": {"$ref": "#/components/schemas/LocationHistorySchema"}, "title": "Locations", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["locations", "total", "page", "page_size"], "title": "LocationHistoryResponseSchema", "type": "object"}, "LocationHistorySchema": {"properties": {"id": {"title": "Id", "type": "string"}, "location_lat": {"title": "Location Lat", "type": "number"}, "location_lng": {"title": "Location Lng", "type": "number"}, "update_time": {"format": "date-time", "title": "Update Time", "type": "string"}}, "required": ["id", "location_lat", "location_lng", "update_time"], "title": "LocationHistorySchema", "type": "object"}, "SubscriptionPlanListResponseSchema": {"properties": {"subscription_plans": {"items": {"$ref": "#/components/schemas/SubscriptionPlanSchema"}, "title": "Subscription Plans", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["subscription_plans", "total", "page", "page_size"], "title": "SubscriptionPlanListResponseSchema", "type": "object"}, "SubscriptionPlanSchema": {"properties": {"id": {"title": "Id", "type": "string"}, "name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "price": {"title": "Price", "type": "number"}, "duration": {"title": "Duration", "type": "integer"}, "duration_unit": {"title": "Duration Unit", "type": "string"}}, "required": ["id", "name", "description", "price", "duration", "duration_unit"], "title": "SubscriptionPlanSchema", "type": "object"}, "CreateSubscriptionPlanSchema": {"properties": {"name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "price": {"title": "Price", "type": "number"}, "duration": {"title": "Duration", "type": "integer"}, "duration_unit": {"title": "Duration Unit", "type": "string"}}, "required": ["name", "description", "price", "duration", "duration_unit"], "title": "CreateSubscriptionPlanSchema", "type": "object"}, "UpdateSubscriptionPlanSchema": {"properties": {"name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "price": {"title": "Price", "type": "number"}, "duration": {"title": "Duration", "type": "integer"}, "duration_unit": {"title": "Duration Unit", "type": "string"}}, "required": ["name", "description", "price", "duration", "duration_unit"], "title": "UpdateSubscriptionPlanSchema", "type": "object"}, "MerchantListResponseSchema": {"properties": {"merchants": {"items": {"$ref": "#/components/schemas/MerchantSchema"}, "title": "Merchants", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["merchants", "total", "page", "page_size"], "title": "MerchantListResponseSchema", "type": "object"}, "MerchantSchema": {"properties": {"id": {"title": "Id", "type": "string"}, "name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "address": {"title": "Address", "type": "string"}, "phone_number": {"title": "Phone Number", "type": "string"}, "email": {"title": "Email", "type": "string"}, "subscription_plan": {"$ref": "#/components/schemas/SubscriptionPlanSchema"}, "subscription_start_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Subscription Start Date"}, "subscription_end_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Subscription End Date"}}, "required": ["id", "name", "description", "address", "phone_number", "email", "subscription_plan", "subscription_start_date", "subscription_end_date"], "title": "MerchantSchema", "type": "object"}, "CreateMerchantSchema": {"properties": {"name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "address": {"title": "Address", "type": "string"}, "phone_number": {"title": "Phone Number", "type": "string"}, "email": {"title": "Email", "type": "string"}, "subscription_plan_id": {"title": "Subscription Plan Id", "type": "string"}}, "required": ["name", "description", "address", "phone_number", "email", "subscription_plan_id"], "title": "CreateMerchantSchema", "type": "object"}, "UpdateMerchantSchema": {"properties": {"name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "address": {"title": "Address", "type": "string"}, "phone_number": {"title": "Phone Number", "type": "string"}, "email": {"title": "Email", "type": "string"}, "subscription_plan_id": {"title": "Subscription Plan Id", "type": "string"}}, "required": ["name", "description", "address", "phone_number", "email", "subscription_plan_id"], "title": "UpdateMerchantSchema", "type": "object"}, "OfficeListResponseSchema": {"properties": {"offices": {"items": {"$ref": "#/components/schemas/OfficeSchema"}, "title": "Offices", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["offices", "total", "page", "page_size"], "title": "OfficeListResponseSchema", "type": "object"}, "OfficeSchema": {"properties": {"id": {"title": "Id", "type": "string"}, "merchant_id": {"title": "Merchant Id", "type": "string"}, "name": {"title": "Name", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "address": {"title": "Address", "type": "string"}, "phone_number": {"title": "Phone Number", "type": "string"}, "email": {"title": "Email", "type": "string"}}, "required": ["id", "merchant_id", "name", "slug", "address", "phone_number", "email"], "title": "OfficeSchema", "type": "object"}, "CreateOfficeSchema": {"properties": {"merchant_id": {"title": "Merchant Id", "type": "string"}, "name": {"title": "Name", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "address": {"title": "Address", "type": "string"}, "phone_number": {"title": "Phone Number", "type": "string"}, "email": {"title": "Email", "type": "string"}}, "required": ["merchant_id", "name", "slug", "address", "phone_number", "email"], "title": "CreateOfficeSchema", "type": "object"}, "UpdateOfficeSchema": {"properties": {"name": {"title": "Name", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "address": {"title": "Address", "type": "string"}, "phone_number": {"title": "Phone Number", "type": "string"}, "email": {"title": "Email", "type": "string"}}, "required": ["name", "slug", "address", "phone_number", "email"], "title": "UpdateOfficeSchema", "type": "object"}, "OfficeEmployeeListResponseSchema": {"properties": {"employees": {"items": {"$ref": "#/components/schemas/OfficeEmployeeSchema"}, "title": "Employees", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["employees", "total", "page", "page_size"], "title": "OfficeEmployeeListResponseSchema", "type": "object"}, "OfficeEmployeeSchema": {"properties": {"id": {"title": "Id", "type": "string"}, "office_id": {"title": "Office Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}, "user": {"$ref": "#/components/schemas/UserSchema"}}, "required": ["id", "office_id", "user_id", "user"], "title": "OfficeEmployeeSchema", "type": "object"}, "CreateOfficeEmployeeSchema": {"properties": {"office_id": {"title": "Office Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}}, "required": ["office_id", "user_id"], "title": "CreateOfficeEmployeeSchema", "type": "object"}, "CompanyListResponseSchema": {"properties": {"companies": {"items": {"$ref": "#/components/schemas/CompanySchema"}, "title": "Companies", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["companies", "total", "page", "page_size"], "title": "CompanyListResponseSchema", "type": "object"}, "CompanySchema": {"properties": {"id": {"title": "Id", "type": "string"}, "office_id": {"title": "Office Id", "type": "string"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}, "name": {"title": "Name", "type": "string"}, "address": {"title": "Address", "type": "string"}, "phone": {"title": "Phone", "type": "string"}, "color_code": {"title": "Color Code", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "office_id", "code", "name", "address", "phone", "color_code", "created_at", "updated_at"], "title": "CompanySchema", "type": "object"}, "CreateCompanySchema": {"properties": {"office_id": {"title": "Office Id", "type": "string"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}, "name": {"title": "Name", "type": "string"}, "address": {"title": "Address", "type": "string"}, "phone": {"title": "Phone", "type": "string"}, "color_code": {"title": "Color Code", "type": "string"}}, "required": ["office_id", "code", "name", "address", "phone", "color_code"], "title": "CreateCompanySchema", "type": "object"}, "UpdateCompanySchema": {"properties": {"code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}, "name": {"title": "Name", "type": "string"}, "address": {"title": "Address", "type": "string"}, "phone": {"title": "Phone", "type": "string"}, "color_code": {"title": "Color Code", "type": "string"}}, "required": ["code", "name", "address", "phone", "color_code"], "title": "UpdateCompanySchema", "type": "object"}, "CompanyChannelListResponseSchema": {"properties": {"channels": {"items": {"$ref": "#/components/schemas/CompanyChannelSchema"}, "title": "Channels", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["channels", "total", "page", "page_size"], "title": "CompanyChannelListResponseSchema", "type": "object"}, "CompanyChannelSchema": {"properties": {"id": {"title": "Id", "type": "string"}, "merchant_id": {"title": "Merchant Id", "type": "string"}, "office_id": {"title": "Office Id", "type": "string"}, "company_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Company Id"}, "name": {"title": "Name", "type": "string"}, "notes": {"title": "Notes", "type": "string"}, "channel_whatsapp_number": {"title": "Channel Whatsapp Number", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "merchant_id", "office_id", "company_id", "name", "notes", "channel_whatsapp_number", "created_at", "updated_at"], "title": "CompanyChannelSchema", "type": "object"}, "CreateCompanyChannelSchema": {"properties": {"merchant_id": {"title": "Merchant Id", "type": "string"}, "office_id": {"title": "Office Id", "type": "string"}, "company_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Company Id"}, "name": {"title": "Name", "type": "string"}, "notes": {"title": "Notes", "type": "string"}, "channel_whatsapp_number": {"title": "Channel Whatsapp Number", "type": "string"}}, "required": ["merchant_id", "office_id", "company_id", "name", "notes", "channel_whatsapp_number"], "title": "CreateCompanyChannelSchema", "type": "object"}, "UpdateCompanyChannelSchema": {"properties": {"company_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Company Id"}, "name": {"title": "Name", "type": "string"}, "notes": {"title": "Notes", "type": "string"}, "channel_whatsapp_number": {"title": "Channel Whatsapp Number", "type": "string"}}, "required": ["company_id", "name", "notes", "channel_whatsapp_number"], "title": "UpdateCompanyChannelSchema", "type": "object"}, "OrderCancellationReasonTemplateListResponseSchema": {"properties": {"templates": {"items": {"$ref": "#/components/schemas/OrderCancellationReasonTemplateSchema"}, "title": "Templates", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["templates", "total", "page", "page_size"], "title": "OrderCancellationReasonTemplateListResponseSchema", "type": "object"}, "OrderCancellationReasonTemplateSchema": {"properties": {"id": {"title": "Id", "type": "string"}, "office_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Office Id"}, "name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "order_default_handling_status": {"anyOf": [{"$ref": "#/components/schemas/OrderHandlingStatusSchema"}, {"type": "null"}]}, "just_delivery_commission_rate": {"title": "Just Delivery Commission Rate", "type": "boolean"}, "commission_fixed_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Commission Fixed Rate"}, "percentage_of_order_total_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Percentage Of Order Total Price"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "office_id", "name", "description", "order_default_handling_status", "just_delivery_commission_rate", "commission_fixed_rate", "percentage_of_order_total_price", "created_at", "updated_at"], "title": "OrderCancellationReasonTemplateSchema", "type": "object"}, "OrderHandlingStatusSchema": {"enum": ["PENDING", "ASSIGNED", "PROCESSING", "CANCELLED", "DELIVERED"], "title": "OrderHandlingStatusSchema", "type": "string"}, "CreateOrderCancellationReasonTemplateSchema": {"properties": {"office_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Office Id"}, "name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "order_default_handling_status": {"anyOf": [{"$ref": "#/components/schemas/OrderHandlingStatusSchema"}, {"type": "null"}]}, "just_delivery_commission_rate": {"title": "Just Delivery Commission Rate", "type": "boolean"}, "commission_fixed_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Commission Fixed Rate"}, "percentage_of_order_total_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Percentage Of Order Total Price"}}, "required": ["office_id", "name", "description", "order_default_handling_status", "just_delivery_commission_rate", "commission_fixed_rate", "percentage_of_order_total_price"], "title": "CreateOrderCancellationReasonTemplateSchema", "type": "object"}, "UpdateOrderCancellationReasonTemplateSchema": {"properties": {"name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "order_default_handling_status": {"anyOf": [{"$ref": "#/components/schemas/OrderHandlingStatusSchema"}, {"type": "null"}]}, "just_delivery_commission_rate": {"title": "Just Delivery Commission Rate", "type": "boolean"}, "commission_fixed_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Commission Fixed Rate"}, "percentage_of_order_total_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Percentage Of Order Total Price"}}, "required": ["name", "description", "order_default_handling_status", "just_delivery_commission_rate", "commission_fixed_rate", "percentage_of_order_total_price"], "title": "UpdateOrderCancellationReasonTemplateSchema", "type": "object"}, "OrderListResponseSchema": {"properties": {"orders": {"items": {"$ref": "#/components/schemas/OrderSchema"}, "title": "Orders", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["orders", "total", "page", "page_size"], "title": "OrderListResponseSchema", "type": "object"}, "OrderSchema": {"properties": {"id": {"title": "Id", "type": "string"}, "office_id": {"title": "Office Id", "type": "string"}, "code": {"title": "Code", "type": "string"}, "notes": {"title": "Notes", "type": "string"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Price"}, "customer_name": {"title": "Customer Name", "type": "string"}, "customer_phone": {"title": "Customer Phone", "type": "string"}, "customer_address": {"title": "Customer Address", "type": "string"}, "customer_company_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Customer Company Id"}, "breakable": {"title": "Breakable", "type": "boolean"}, "deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Deadline Date"}, "commission_fixed_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Commission Fixed Rate"}, "assigned_to_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned To Id"}, "assigned_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Assigned At"}, "final_customer_payment": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Final Customer Payment"}, "handling_status": {"$ref": "#/components/schemas/OrderHandlingStatusSchema"}, "cancellation_reason_template_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cancellation Reason Template Id"}, "cancellation_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cancellation Reason"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "office_id", "code", "notes", "total_price", "customer_name", "customer_phone", "customer_address", "customer_company_id", "breakable", "deadline_date", "commission_fixed_rate", "assigned_to_id", "assigned_at", "final_customer_payment", "handling_status", "cancellation_reason_template_id", "cancellation_reason", "created_at", "updated_at"], "title": "OrderSchema", "type": "object"}, "CreateOrderSchema": {"properties": {"office_id": {"title": "Office Id", "type": "string"}, "code": {"title": "Code", "type": "string"}, "notes": {"title": "Notes", "type": "string"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Price"}, "customer_name": {"title": "Customer Name", "type": "string"}, "customer_phone": {"title": "Customer Phone", "type": "string"}, "customer_address": {"title": "Customer Address", "type": "string"}, "customer_company_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Customer Company Id"}, "breakable": {"title": "Breakable", "type": "boolean"}, "deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Deadline Date"}, "commission_fixed_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Commission Fixed Rate"}, "assigned_to_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned To Id"}, "final_customer_payment": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Final Customer Payment"}, "handling_status": {"$ref": "#/components/schemas/OrderHandlingStatusSchema"}, "cancellation_reason_template_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cancellation Reason Template Id"}, "cancellation_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cancellation Reason"}}, "required": ["office_id", "code", "notes", "total_price", "customer_name", "customer_phone", "customer_address", "customer_company_id", "breakable", "deadline_date", "commission_fixed_rate", "assigned_to_id", "final_customer_payment", "handling_status", "cancellation_reason_template_id", "cancellation_reason"], "title": "CreateOrderSchema", "type": "object"}, "UpdateOrderSchema": {"properties": {"code": {"title": "Code", "type": "string"}, "notes": {"title": "Notes", "type": "string"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Price"}, "customer_name": {"title": "Customer Name", "type": "string"}, "customer_phone": {"title": "Customer Phone", "type": "string"}, "customer_address": {"title": "Customer Address", "type": "string"}, "customer_company_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Customer Company Id"}, "breakable": {"title": "Breakable", "type": "boolean"}, "deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Deadline Date"}, "commission_fixed_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Commission Fixed Rate"}, "assigned_to_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned To Id"}, "final_customer_payment": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Final Customer Payment"}, "handling_status": {"$ref": "#/components/schemas/OrderHandlingStatusSchema"}, "cancellation_reason_template_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cancellation Reason Template Id"}, "cancellation_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cancellation Reason"}}, "required": ["code", "notes", "total_price", "customer_name", "customer_phone", "customer_address", "customer_company_id", "breakable", "deadline_date", "commission_fixed_rate", "assigned_to_id", "final_customer_payment", "handling_status", "cancellation_reason_template_id", "cancellation_reason"], "title": "UpdateOrderSchema", "type": "object"}, "OrderStatusHistoryListResponseSchema": {"properties": {"history": {"items": {"$ref": "#/components/schemas/OrderStatusHistorySchema"}, "title": "History", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["history", "total", "page", "page_size"], "title": "OrderStatusHistoryListResponseSchema", "type": "object"}, "OrderStatusHistorySchema": {"properties": {"id": {"title": "Id", "type": "string"}, "order_id": {"title": "Order Id", "type": "string"}, "status": {"$ref": "#/components/schemas/OrderHandlingStatusSchema"}, "notes": {"title": "Notes", "type": "string"}, "created_by_id": {"title": "Created By Id", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "order_id", "status", "notes", "created_by_id", "created_at", "updated_at"], "title": "OrderStatusHistorySchema", "type": "object"}, "OrderAssigneeHistoryListResponseSchema": {"properties": {"history": {"items": {"$ref": "#/components/schemas/OrderAssigneeHistorySchema"}, "title": "History", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}}, "required": ["history", "total", "page", "page_size"], "title": "OrderAssigneeHistoryListResponseSchema", "type": "object"}, "OrderAssigneeHistorySchema": {"properties": {"id": {"title": "Id", "type": "string"}, "order_id": {"title": "Order Id", "type": "string"}, "assignee_id": {"title": "Assignee <PERSON>", "type": "string"}, "assigned_by_id": {"title": "Assigned By Id", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "order_id", "assignee_id", "assigned_by_id", "created_at", "updated_at"], "title": "OrderAssigneeHistorySchema", "type": "object"}}, "securitySchemes": {"ApiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "name": "token"}}}, "servers": []}