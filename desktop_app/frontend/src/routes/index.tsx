import { createFileRoute } from '@tanstack/react-router'
import logo from '../logo.svg'

export const Route = createFileRoute('/')({
  component: HomePage,
})

function HomePage() {
  return (
    <div className="min-vh-100 bg-light">
      {/* Navigation */}
      <nav className="navbar navbar-expand-lg navbar-dark bg-primary">
        <div className="container">
          <a className="navbar-brand fw-bold" href="#">
            <img src={logo} alt="Logo" width="30" height="30" className="d-inline-block align-text-top me-2" />
            MyRunway
          </a>
          <button className="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span className="navbar-toggler-icon"></span>
          </button>
          <div className="collapse navbar-collapse" id="navbarNav">
            <ul className="navbar-nav ms-auto">
              <li className="nav-item">
                <a className="nav-link active" href="#">Home</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#">Features</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#">About</a>
              </li>
              <li className="nav-item">
                <a className="nav-link" href="#">Contact</a>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="container-fluid bg-primary text-white py-5">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6">
              <h1 className="display-4 fw-bold mb-4">Welcome to MyRunway</h1>
              <p className="lead mb-4">
                Your comprehensive platform for managing orders, merchants, and accounts with ease.
                Built with modern technologies for optimal performance.
              </p>
              <div className="d-flex gap-3">
                <button className="btn btn-light btn-lg px-4">Get Started</button>
                <button className="btn btn-outline-light btn-lg px-4">Learn More</button>
              </div>
            </div>
            <div className="col-lg-6 text-center">
              <img src={logo} alt="Hero" className="img-fluid" style={{ maxWidth: '300px' }} />
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="container py-5">
        <div className="row text-center mb-5">
          <div className="col-12">
            <h2 className="display-5 fw-bold">Key Features</h2>
            <p className="lead text-muted">Everything you need to manage your business efficiently</p>
          </div>
        </div>
        <div className="row g-4">
          <div className="col-md-4">
            <div className="card h-100 border-0 shadow-sm">
              <div className="card-body text-center p-4">
                <div className="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style={{ width: '60px', height: '60px' }}>
                  <i className="bi bi-cart text-primary fs-3"></i>
                </div>
                <h5 className="card-title">Order Management</h5>
                <p className="card-text text-muted">
                  Streamline your order processing with our comprehensive management system.
                </p>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card h-100 border-0 shadow-sm">
              <div className="card-body text-center p-4">
                <div className="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style={{ width: '60px', height: '60px' }}>
                  <i className="bi bi-shop text-success fs-3"></i>
                </div>
                <h5 className="card-title">Merchant Portal</h5>
                <p className="card-text text-muted">
                  Manage your merchant accounts and business operations seamlessly.
                </p>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card h-100 border-0 shadow-sm">
              <div className="card-body text-center p-4">
                <div className="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style={{ width: '60px', height: '60px' }}>
                  <i className="bi bi-person-circle text-info fs-3"></i>
                </div>
                <h5 className="card-title">Account Management</h5>
                <p className="card-text text-muted">
                  Secure and efficient user account management with advanced features.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="container-fluid bg-dark text-white py-5">
        <div className="container">
          <div className="row text-center">
            <div className="col-md-3 mb-3">
              <h3 className="display-6 fw-bold text-primary">1000+</h3>
              <p className="text-muted">Active Orders</p>
            </div>
            <div className="col-md-3 mb-3">
              <h3 className="display-6 fw-bold text-success">500+</h3>
              <p className="text-muted">Merchants</p>
            </div>
            <div className="col-md-3 mb-3">
              <h3 className="display-6 fw-bold text-info">10K+</h3>
              <p className="text-muted">Users</p>
            </div>
            <div className="col-md-3 mb-3">
              <h3 className="display-6 fw-bold text-warning">99.9%</h3>
              <p className="text-muted">Uptime</p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-light py-4">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center">
              <p className="text-muted mb-0">
                © 2024 MyRunway. Built with React, TanStack Router, and Bootstrap.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
