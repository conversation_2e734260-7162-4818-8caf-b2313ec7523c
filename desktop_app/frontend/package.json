{"name": "frontend", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run"}, "dependencies": {"@popperjs/core": "^2.11.8", "@tanstack/react-router": "^1.121.2", "@tanstack/react-router-devtools": "^1.121.2", "@tanstack/router-plugin": "^1.121.2", "@types/bootstrap": "^5.2.10", "bootstrap": "^5.3.7", "bootstrap-icons": "^1.13.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@hey-api/openapi-ts": "0.79.1", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/node": "^24.0.15", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "typescript": "^5.7.2", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}